/**
 * Test script for Cashfree payment verification
 * 
 * This script helps test the Cashfree payment flow:
 * 1. Create a deposit order
 * 2. Simulate payment completion
 * 3. Verify the payment
 * 
 * Usage:
 * 1. Make sure your ScrapHub app is running
 * 2. Update the BASE_URL and authentication details below
 * 3. Run: node test-cashfree-verification.js
 */

const BASE_URL = 'http://localhost:3000'; // Update this to your ScrapHub URL
const API_ENDPOINT = `${BASE_URL}/api/trpc`;

// Test configuration
const TEST_CONFIG = {
  // Update these with your test credentials
  email: '<EMAIL>',
  password: 'your-password', // Update this
  amount: 100, // Test amount in rupees
  currency: 'INR'
};

async function makeRequest(procedure, input, sessionCookie = null) {
  const url = `${API_ENDPOINT}/scraphub.payment.${procedure}`;
  
  const headers = {
    'Content-Type': 'application/json',
  };
  
  if (sessionCookie) {
    headers['Cookie'] = sessionCookie;
  }
  
  const response = await fetch(url, {
    method: 'POST',
    headers,
    body: JSON.stringify({ json: input })
  });
  
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`HTTP ${response.status}: ${errorText}`);
  }
  
  return response.json();
}

async function testCashfreeFlow() {
  console.log('🚀 Starting Cashfree payment verification test...\n');
  
  try {
    // Step 1: Create a deposit order
    console.log('📝 Step 1: Creating deposit order...');
    const orderResponse = await makeRequest('createDepositOrder', {
      amount: TEST_CONFIG.amount,
      currency: TEST_CONFIG.currency
    });
    
    console.log('✅ Order created successfully:');
    console.log('   Order ID:', orderResponse.result.data.json.orderId);
    console.log('   Transaction ID:', orderResponse.result.data.json.transactionId);
    console.log('   Payment Session ID:', orderResponse.result.data.json.payment_session_id || 'Not available');
    console.log('   Gateway:', orderResponse.result.data.json.gateway);
    console.log('   Amount:', orderResponse.result.data.json.amount);
    
    const { orderId, transactionId, gateway } = orderResponse.result.data.json;
    
    // Step 2: Instructions for manual payment
    console.log('\n💳 Step 2: Complete the payment manually');
    console.log('   1. Use the Cashfree payment interface to complete the payment');
    console.log('   2. Note down the Cashfree Payment ID (cf_payment_id)');
    console.log('   3. Come back here and update the CASHFREE_PAYMENT_ID below');
    console.log('   4. Uncomment and run the verification step');
    
    // Step 3: Payment verification (uncomment after completing payment)
    /*
    const CASHFREE_PAYMENT_ID = 'your_cashfree_payment_id_here'; // Update this
    
    console.log('\n🔍 Step 3: Verifying payment...');
    const verifyResponse = await makeRequest('verifyDeposit', {
      transactionId: transactionId,
      cashfreeOrderId: orderId,
      cashfreePaymentId: CASHFREE_PAYMENT_ID
    });
    
    console.log('✅ Payment verification result:');
    console.log(JSON.stringify(verifyResponse, null, 2));
    */
    
    console.log('\n📋 Test Summary:');
    console.log('   ✅ Order creation: SUCCESS');
    console.log('   ⏳ Payment completion: MANUAL STEP REQUIRED');
    console.log('   ⏳ Payment verification: PENDING');
    
    console.log('\n🔧 Next Steps:');
    console.log('   1. Complete the payment using Cashfree interface');
    console.log('   2. Get the cf_payment_id from Cashfree');
    console.log('   3. Update this script with the payment ID');
    console.log('   4. Uncomment the verification section and run again');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
}

// Helper function to test just the verification part
async function testVerificationOnly(transactionId, cashfreePaymentId) {
  console.log('🔍 Testing payment verification only...\n');
  
  try {
    const verifyResponse = await makeRequest('verifyDeposit', {
      transactionId: transactionId,
      cashfreePaymentId: cashfreePaymentId
    });
    
    console.log('✅ Verification response:');
    console.log(JSON.stringify(verifyResponse, null, 2));
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    console.error('Full error:', error);
  }
}

// Run the test
if (require.main === module) {
  testCashfreeFlow();
}

module.exports = {
  testCashfreeFlow,
  testVerificationOnly,
  makeRequest
};
