import { Cashfree, CashfreeCreateTransferRequest } from "@acme/cashfree-sdk";

import type {
  BillResponse,
  ContactResponse,
  CreateBillRequest,
  CreateContactRequest,
  CreateFundAccountRequest,
  CreateOrderRequest,
  CreatePayoutRequest,
  FundAccountResponse,
  IPaymentGateway,
  OrderResponse,
  PaymentResponse,
  PayoutResponse,
  ValidateFundAccountRequest,
  ValidateFundAccountResponse,
  VerifyPaymentRequest,
  VerifyPaymentResponse,
} from "../types";

export class CashfreeAdapter implements IPaymentGateway {
  private cashfree: Cashfree;

  constructor(config: {
    appId: string;
    secretKey: string;
    environment: "sandbox" | "production";
  }) {
    this.cashfree = new Cashfree(config);
  }

  async createOrder(
    request: CreateOrderRequest,
  ): Promise<PaymentResponse<OrderResponse>> {
    try {
      const cashfreeRequest = {
        order_id: request.receipt || `order_${Date.now()}`,
        order_amount: request.amount,
        order_currency: request.currency,
        customer_details: {
          customer_id: request.customer_id,
          customer_name: request.notes?.customer_name,
          customer_email: request.notes?.customer_email,
          customer_phone: request.notes?.customer_phone,
        },
        order_note: request.notes?.description,
        order_tags: request.notes,
      };

      const response = await this.cashfree.createOrder(cashfreeRequest);

      if (response.error || !response.data) {
        return { data: null, isLoading: false, error: response.error };
      }

      // Extract data from Cashfree response
      const cashfreeData = response.data as any;
      const paymentSessionId =
        cashfreeData.payment_session_id || cashfreeData.order_token;

      if (!paymentSessionId) {
        console.error(
          "Cashfree order response missing payment_session_id:",
          cashfreeData,
        );
        throw new Error("Payment session ID is required from Cashfree");
      }

      console.log("Cashfree order created successfully:", {
        cf_order_id: cashfreeData.cf_order_id,
        order_id: cashfreeData.order_id,
        payment_session_id: paymentSessionId,
        order_status: cashfreeData.order_status,
        order_amount: cashfreeData.order_amount,
      });

      const orderResponse: OrderResponse = {
        id: cashfreeData.cf_order_id,
        amount: cashfreeData.order_amount,
        currency: cashfreeData.order_currency,
        receipt: cashfreeData.order_id,
        status: cashfreeData.order_status,
        created_at: new Date(cashfreeData.created_at).getTime() / 1000,
        payment_session_id: paymentSessionId,
        notes: cashfreeData.order_tags || {},
      };

      return { data: orderResponse, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async verifyPayment(
    request: VerifyPaymentRequest,
  ): Promise<PaymentResponse<VerifyPaymentResponse & { metadata?: any }>> {
    try {
      console.log("Cashfree verifyPayment request:", {
        orderId: request.orderId,
        paymentId: request.paymentId,
      });

      // For Cashfree, we need to get payment details to verify
      const paymentResponse = await this.cashfree.getOrder(request.orderId);

      console.log("Cashfree getPaymentDetails response:", {
        error: paymentResponse.error?.message,
        data: paymentResponse.data,
      });

      if (paymentResponse.error || !paymentResponse.data) {
        console.error(
          "Failed to get Cashfree payment details:",
          paymentResponse.error,
        );
        return {
          data: {
            verified: false,
            paymentId: request.paymentId,
            orderId: request.orderId,
          },
          isLoading: false,
          error: paymentResponse.error,
        };
      }

      // // Find the specific payment
      // const payment = paymentResponse.data.payments.find(
      //   (p) => p.cf_payment_id === request.paymentId,
      // );

      // console.log("Found payment:", {
      //   paymentFound: !!payment,
      //   paymentStatus: payment?.payment_status,
      //   cf_payment_id: payment?.cf_payment_id,
      // });

      const payment = paymentResponse.data;

      const verified =
        payment.order_status === "PAID" || payment.order_status === "SUCCESS";

      // Prepare comprehensive metadata
      const metadata = {
        cf_order_id: payment.cf_order_id,
        payment_status: payment.order_status,
        payment_message: payment.order_note,
        payment_amount: payment.order_amount,
        payment_currency: payment.order_currency,
        bank_reference: payment.order_note,
        auth_id: payment.order_note,
        payment_method: payment.order_note,
        raw_payment_data: payment,
      };

      const verifyResponse: VerifyPaymentResponse & { metadata?: any } = {
        verified: !!verified,
        orderId: payment.cf_order_id || "",
        metadata,
      };

      console.log("Cashfree verification result:", {
        verified,
        paymentId: request.paymentId,
        orderId: request.orderId,
        hasMetadata: !!metadata,
      });

      return { data: verifyResponse, isLoading: false, error: null };
    } catch (error) {
      console.error("Cashfree verifyPayment error:", error);
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async createContact(
    request: CreateContactRequest,
  ): Promise<PaymentResponse<ContactResponse>> {
    try {
      const cashfreeRequest = {
        bene_id: request.reference_id || `contact_${Date.now()}`,
        name: request.name,
        email: request.email,
        phone: request.contact,
      };

      const response = await this.cashfree.createBeneficiary(cashfreeRequest);

      if (response.error) {
        return { data: null, isLoading: false, error: response.error };
      }

      const contactResponse: ContactResponse = {
        id: response.data!.bene_id,
        entity: "contact",
        name: response.data!.name,
        contact: response.data!.phone,
        email: response.data!.email,
        type: request.type || "customer",
        reference_id: response.data!.bene_id,
        active: response.data!.status === "ACTIVE",
        notes: request.notes || {},
        created_at: new Date(response.data!.added_on).getTime() / 1000,
      };

      return { data: contactResponse, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async createFundAccount(
    request: CreateFundAccountRequest,
  ): Promise<PaymentResponse<FundAccountResponse>> {
    try {
      // For Cashfree, beneficiary creation includes fund account details
      const cashfreeRequest = {
        bene_id: `${request.contact_id}_${Date.now()}`,
        name: request.bank_account?.name || request.vpa?.address || "Unknown",
        email: "",
        phone: "",
        ...(request.bank_account && {
          bank_account: request.bank_account.account_number,
          ifsc: request.bank_account.ifsc,
        }),
        ...(request.vpa && {
          vpa: request.vpa.address,
        }),
      };

      const response = await this.cashfree.createBeneficiary(cashfreeRequest);

      if (response.error) {
        return { data: null, isLoading: false, error: response.error };
      }

      const fundAccountResponse: FundAccountResponse = {
        id: response.data!.bene_id,
        entity: "fund_account",
        contact_id: request.contact_id,
        account_type: request.account_type,
        ...(request.bank_account && {
          bank_account: {
            name: response.data!.name,
            account_number: response.data!.bank_account || "",
            ifsc: response.data!.ifsc || "",
            bank_name: "",
          },
        }),
        ...(request.vpa && {
          vpa: {
            address: response.data!.vpa || "",
            handle: "",
          },
        }),
        active: response.data!.status === "ACTIVE",
        created_at: new Date(response.data!.added_on).getTime() / 1000,
      };

      return { data: fundAccountResponse, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async getAllFundAccounts(
    contactId: string,
  ): Promise<PaymentResponse<FundAccountResponse[]>> {
    // Cashfree doesn't have a direct equivalent, return empty array
    return { data: [], isLoading: false, error: null };
  }

  async deactivateFundAccount(
    fundAccountId: string,
  ): Promise<PaymentResponse<FundAccountResponse>> {
    try {
      const response = await this.cashfree.removeBeneficiary(fundAccountId);

      if (response.error) {
        return { data: null, isLoading: false, error: response.error };
      }

      // Return a mock response since Cashfree doesn't return the updated beneficiary
      const fundAccountResponse: FundAccountResponse = {
        id: fundAccountId,
        entity: "fund_account",
        contact_id: "",
        account_type: "bank_account",
        active: false,
        created_at: Date.now() / 1000,
      };

      return { data: fundAccountResponse, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async reactivateFundAccount(
    fundAccountId: string,
  ): Promise<PaymentResponse<FundAccountResponse>> {
    // Cashfree doesn't support reactivation, would need to recreate
    return {
      data: null,
      isLoading: false,
      error: new Error("Cashfree doesn't support fund account reactivation"),
    };
  }

  async createPayout(
    request: CreatePayoutRequest,
  ): Promise<PaymentResponse<PayoutResponse>> {
    try {
      const transferMode: CashfreeCreateTransferRequest["transfer_mode"] =
        request.mode === "UPI" ? "upi" : "banktransfer";
      const cashfreeRequest = {
        bene_id: request.beneficiary_id,
        fundsource_id: request.fundsource_id,
        amount: request.amount,
        transfer_id: request.reference_id || `transfer_${Date.now()}`,
        transfer_mode: transferMode,
        remarks: request.narration || request.purpose,
      };

      const response = await this.cashfree.createTransfer(cashfreeRequest);

      if (response.error) {
        return { data: null, isLoading: false, error: response.error };
      }

      const payoutResponse: PayoutResponse = {
        id: response.data!.cf_transfer_id,
        entity: "payout",
        fund_account_id: response.data!.beneficiary_details.beneficiary_id,
        amount: response.data!.transfer_amount,
        currency: request.currency,
        notes: request.notes || {},
        fees: 0, // Cashfree doesn't provide fees in response
        tax: 0,
        status: response.data!.status,
        purpose: request.purpose,
        utr: response.data!.transfer_utr,
        mode: response.data!.transfer_mode,
        reference_id: response.data!.transfer_id,
        // narration: response.data!.remarks,
        created_at: new Date(response.data!.added_on).getTime() / 1000,
      };

      return { data: payoutResponse, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async createBill(
    request: CreateBillRequest,
  ): Promise<PaymentResponse<BillResponse>> {
    // Cashfree doesn't have a direct bill creation API like Razorpay
    return {
      data: null,
      isLoading: false,
      error: new Error("Cashfree doesn't support bill creation"),
    };
  }

  async validateFundAccount(
    request: ValidateFundAccountRequest,
  ): Promise<PaymentResponse<ValidateFundAccountResponse>> {
    // Cashfree doesn't have fund account validation like Razorpay
    return {
      data: null,
      isLoading: false,
      error: new Error("Cashfree doesn't support fund account validation"),
    };
  }
}
