import { TRPCError } from "@trpc/server";
import { asc, desc, eq, inArray, isNull } from "drizzle-orm";
import { z } from "zod";

import { and } from "@acme/db";
import {
  kabadiwala,
  kabadiwalaPaymentTransaction,
  order,
  orderItem,
  scraphubEmployee,
  systemConfiguration,
} from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { createTRPCRouter, protectedProcedure } from "../trpc";

export const orderRouter = createTRPCRouter({
  getAllOrders: protectedProcedure
    .input(
      z
        .object({
          status: z.enum(["ALL", "PENDING", "COMPLETED"]),
        })
        .default({ status: "ALL" }),
    )
    .query(async ({ ctx, input }) => {
      // First, get the scraphub employee's scraphubId
      const { data: employeeData, err: employeeErr } = await tryCatch(
        ctx.db.query.scraphubEmployee.findFirst({
          where: eq(scraphubEmployee.id, ctx.session.user.id),
          columns: {
            scraphubId: true,
          },
        }),
      );

      if (employeeErr || !employeeData) {
        console.error("Failed to fetch employee data:", employeeErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch employee data",
        });
      }

      // Get all kabadiwalas assigned to this scraphub
      const { data: assignedKabadiwalas, err: kabadiwalasErr } = await tryCatch(
        ctx.db.query.kabadiwala.findMany({
          where: eq(kabadiwala.scraphubId, employeeData.scraphubId ?? ""),
          columns: {
            id: true,
          },
        }),
      );

      if (kabadiwalasErr) {
        console.error("Failed to fetch assigned kabadiwalas:", kabadiwalasErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch assigned kabadiwalas",
        });
      }

      // Extract kabadiwala IDs
      const kabadiwalaIds = assignedKabadiwalas.map((k) => k.id);

      // If no kabadiwalas are assigned to this scraphub, return empty array
      if (kabadiwalaIds.length === 0) {
        return [];
      }

      const whereCondition =
        input.status === "ALL"
          ? and(
              eq(order.status, "COMPLETED"),
              isNull(order.afterCompletedStatus),
              isNull(order.scraphubEmployeeId),
              inArray(order.kabadiwalaId, kabadiwalaIds),
            )
          : input.status === "PENDING" // this pending means order is assigned to scraphub employee but not yet completed
            ? and(
                eq(order.afterCompletedStatus, "WITH_KABADIWALA"),
                eq(order.scraphubEmployeeId, ctx.session.user.id!),
                inArray(order.kabadiwalaId, kabadiwalaIds),
              )
            : and(
                eq(order.afterCompletedStatus, "WITH_SCRAPHUB"),
                eq(order.scraphubEmployeeId, ctx.session.user.id!),
                inArray(order.kabadiwalaId, kabadiwalaIds),
              );

      const { data, err } = await tryCatch(
        ctx.db.query.order.findMany({
          columns: {
            id: true,
            status: true,
            totalAmount: true,
            createdAt: true,
            sellerId: true,
            kabadiwalaId: true,
            addressId: true,
            pickupOtpStatus: true,
            completedAt: true,
            scraphubEmployeeId: true,
          },
          where: whereCondition,
          orderBy: desc(order.createdAt),
          with: {
            seller: {
              columns: {
                id: true,
                fullName: true,
              },
            },
            kabadiwala: {
              columns: {
                id: true,
                name: true,
              },
            },
            address: true,
          },
        }),
      );

      if (err) {
        console.error("Failed to fetch orders:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch orders",
        });
      }

      return data;
    }),

  getOrderById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.id),
          with: {
            address: true,
            kabadiwala: {
              columns: {
                id: true,
                name: true,
                image: true,
                isOnDuty: true,
                email: true,
                phoneNumber: true,
                emailVerified: true,
                phoneNumberVerified: true,
                liveLocationCoordinate: true,
                walletBalance: true,
              },
            },
            seller: {
              columns: {
                id: true,
                fullName: true,
                image: true,
                email: true,
                phoneNumber: true,
                emailVerified: true,
                phoneNumberVerified: true,
              },
            },
            items: {
              with: {
                category: true,
              },
              orderBy: asc(orderItem.quantity),
            },
          },
        }),
      );

      if (err || !data) {
        console.error("Failed to fetch order:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch order",
        });
      }

      if (data.kabadiwalaId === null) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Kabadiwala not found for this order",
        });
      }

      let valueOfItemsReceivedAtScraphub = 0;
      data.items.forEach((item) => {
        valueOfItemsReceivedAtScraphub +=
          Number(item.quantityAtScraphub ?? 0) * Number(item.category.rate);
      });

      let amountToBePaidToKabadiwala = 0;
      let kabadiwalaTotalCompensation = 0;
      data.items.forEach((item) => {
        amountToBePaidToKabadiwala +=
          Number(item.category.compensationKabadiwalaRate) *
          Number(item.quantityAtScraphub);
        kabadiwalaTotalCompensation +=
          Number(item.category.compensationKabadiwalaRate) *
          Number(item.quantityAtScraphub);
      });

      amountToBePaidToKabadiwala += Number(data.securityFeeAmount);

      const { data: fixedKabadiwalaEarning, err: fixedKabadiwalaEarningErr } =
        await tryCatch(
          ctx.db.query.systemConfiguration.findFirst({
            where: eq(systemConfiguration.key, "FIXED_KABADIWALA_EARNING"),
          }),
        );

      if (fixedKabadiwalaEarningErr) {
        console.error(
          "Failed to fetch fixed kabadiwala earning:",
          fixedKabadiwalaEarningErr,
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch fixed kabadiwala earning",
        });
      }

      amountToBePaidToKabadiwala += Number(fixedKabadiwalaEarning?.value ?? 0);

      return {
        ...data,
        valueOfItemsReceivedAtScraphub,
        amountToBePaidToKabadiwala,
        kabadiwalaTotalCompensation,
        fixedKabadiwalaEarning: Number(fixedKabadiwalaEarning?.value ?? 0),
      };
    }),

  verifyOrderItemAtScraphub: protectedProcedure
    .input(
      z.object({
        orderId: z.string(),
        orderItemId: z.string(),
        quantityAtScraphub: z.number().min(0),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data: orderDetails, err: orderError } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          columns: {
            id: true,
            status: true,
            afterCompletedStatus: true,
            scraphubEmployeeId: true,
            paymentCompletedAt: true,
          },
        }),
      );

      if (orderError || !orderDetails) {
        console.error("Failed to fetch order details:", orderError);
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }

      if (
        orderDetails.status !== "COMPLETED" ||
        orderDetails.paymentCompletedAt === null
      ) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Order is not in a valid state for verification",
        });
      }

      if (orderDetails.scraphubEmployeeId === null) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Order is not assigned to a Scraphub employee",
        });
      }

      if (orderDetails.scraphubEmployeeId !== ctx.session.user.id) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to verify this order items",
        });
      }

      const { err: orderItemVerificationErr } = await tryCatch(
        ctx.db
          .update(orderItem)
          .set({
            quantityAtScraphub: String(input.quantityAtScraphub),
          })
          .where(eq(orderItem.id, input.orderItemId)),
      );

      if (orderItemVerificationErr) {
        console.error("Failed to verify order item:", orderItemVerificationErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to verify order item",
        });
      }

      return {
        message: `Order item verified successfully`,
      };
    }),

  confirmPayment: protectedProcedure
    .input(
      z.object({ orderId: z.string().min(1), kabadiwalaId: z.string().min(1) }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data: orderDetails, err: orderDetailsErr } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          columns: {
            id: true,
            status: true,
            afterCompletedStatus: true,
            scraphubEmployeeId: true,
            paymentCompletedAt: true,
            kabadiwalaId: true,
            securityFeeAmount: true,
          },
          with: {
            items: {
              columns: {
                categoryId: true,
                quantityAtScraphub: true,
              },
              with: {
                category: {
                  columns: {
                    rate: true,
                    compensationKabadiwalaRate: true,
                  },
                },
              },
            },
          },
        }),
      );

      if (orderDetailsErr || !orderDetails) {
        console.error("Failed to fetch order details:", orderDetailsErr);
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }

      if (
        orderDetails.status !== "COMPLETED" ||
        orderDetails.paymentCompletedAt === null
      ) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Order is not in a valid state for confirmation",
        });
      }

      if (orderDetails.scraphubEmployeeId === null) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Order is not assigned to a Scraphub employee",
        });
      }

      if (orderDetails.scraphubEmployeeId !== ctx.session.user.id) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to confirm this order",
        });
      }

      if (
        orderDetails.kabadiwalaId === null ||
        orderDetails.kabadiwalaId !== input.kabadiwalaId
      ) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Order is not assigned to a Kabadiwala",
        });
      }

      const { data: fixedKabadiwalaEarning, err: fixedKabadiwalaEarningErr } =
        await tryCatch(
          ctx.db.query.systemConfiguration.findFirst({
            where: eq(systemConfiguration.key, "FIXED_KABADIWALA_EARNING"),
            columns: {
              value: true,
            },
          }),
        );

      if (fixedKabadiwalaEarningErr) {
        console.error(
          "Failed to fetch fixed kabadiwala earning:",
          fixedKabadiwalaEarningErr,
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch fixed kabadiwala earning",
        });
      }

      const {
        data: kabadiwalaPrevWalletBalance,
        err: kabadiwalaPrevWalletBalanceErr,
      } = await tryCatch(
        ctx.db.query.kabadiwala.findFirst({
          where: eq(kabadiwala.id, input.kabadiwalaId),
          columns: {
            walletBalance: true,
          },
        }),
      );

      if (kabadiwalaPrevWalletBalanceErr || !kabadiwalaPrevWalletBalance) {
        console.error(
          "Failed to fetch kabadiwala wallet balance:",
          kabadiwalaPrevWalletBalanceErr,
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch kabadiwala wallet balance",
        });
      }

      const kabadiwalaWalletBalance = Number(
        kabadiwalaPrevWalletBalance.walletBalance,
      );

      let kabadiwalaEarnings = 0;
      let valueOfItemsReceivedAtScraphubWithSecurityFees = 0;

      orderDetails.items.forEach((item) => {
        kabadiwalaEarnings +=
          Number(item.category.compensationKabadiwalaRate) *
          Number(item.quantityAtScraphub);
      });
      orderDetails.items.forEach((item) => {
        valueOfItemsReceivedAtScraphubWithSecurityFees +=
          Number(item.quantityAtScraphub ?? 0) * Number(item.category.rate);
      });

      valueOfItemsReceivedAtScraphubWithSecurityFees += Number(
        orderDetails.securityFeeAmount,
      );
      kabadiwalaEarnings += Number(fixedKabadiwalaEarning?.value ?? 0);

      const totalAmountToBeUpdatedInKabadiwalaWallet = String(
        Number(kabadiwalaWalletBalance) +
          valueOfItemsReceivedAtScraphubWithSecurityFees +
          kabadiwalaEarnings,
      );

      const { err: paymentErr } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          await tx.insert(kabadiwalaPaymentTransaction).values({
            kabadiwalaId: input.kabadiwalaId,
            orderId: input.orderId,
            amount: String(kabadiwalaEarnings),
            transactionFor: "EARNINGS",
            currency: "INR",
            transactionType: "CREDIT",
            status: "COMPLETED",
          });

          await tx.insert(kabadiwalaPaymentTransaction).values({
            kabadiwalaId: input.kabadiwalaId,
            orderId: input.orderId,
            amount: String(valueOfItemsReceivedAtScraphubWithSecurityFees),
            transactionFor: "ORDER_PAYMENT",
            currency: "INR",
            transactionType: "CREDIT",
            status: "COMPLETED",
          });

          await tx
            .update(order)
            .set({
              afterCompletedStatus: "WITH_SCRAPHUB",
              kabadiwalaEarningsAfterDeliveringOrderAtScraphub:
                String(kabadiwalaEarnings),
              kabadiwalaPaymentRecievedAtScraphubWithoutEarnings: String(
                valueOfItemsReceivedAtScraphubWithSecurityFees,
              ),
              scraphubPaymentAt: new Date(),
            })
            .where(eq(order.id, input.orderId));

          await tx
            .update(kabadiwala)
            .set({
              walletBalance: totalAmountToBeUpdatedInKabadiwalaWallet,
            })
            .where(eq(kabadiwala.id, input.kabadiwalaId));
        }),
      );

      if (paymentErr) {
        console.error("Failed to confirm payment:", paymentErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to confirm payment",
        });
      }

      return {
        message: "Payment confirmed successfully",
      };
    }),

  assignOrderToScraphubEmployee: protectedProcedure
    .input(
      z.object({
        orderId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data: orderDetails, err: orderDetailsErr } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          columns: {
            id: true,
            status: true,
            afterCompletedStatus: true,
            scraphubEmployeeId: true,
            paymentCompletedAt: true,
          },
        }),
      );

      if (orderDetailsErr || !orderDetails) {
        console.error("Failed to fetch order details:", orderDetailsErr);
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }

      if (
        orderDetails.status !== "COMPLETED" ||
        orderDetails.paymentCompletedAt === null
      ) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Order is not in a valid state for assignment",
        });
      }

      if (
        orderDetails.afterCompletedStatus !== null ||
        orderDetails.scraphubEmployeeId !== null
      ) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Order has already been assigned",
        });
      }

      const { data, err } = await tryCatch(
        ctx.db
          .update(order)
          .set({
            afterCompletedStatus: "WITH_KABADIWALA",
            scraphubEmployeeId: ctx.session.user.id,
          })
          .where(eq(order.id, input.orderId))
          .returning(),
      );

      if (err || data.length === 0) {
        console.error("Failed to assign order:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to assign order",
        });
      }

      return data;
    }),
});
