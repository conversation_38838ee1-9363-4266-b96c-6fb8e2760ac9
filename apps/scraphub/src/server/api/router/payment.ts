import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { eq, or, sql } from "@acme/db";
import {
  scraphub,
  scraphubEmployee,
  scraphubPaymentTransaction,
} from "@acme/db/schema";
import {
  createPaymentConfig,
  CustomerManager,
  UnifiedPaymentService,
} from "@acme/payments";
import { tryCatch } from "@acme/validators/utils";

import { env } from "../../../env";
import analytics from "../../../lib/analytics";
import { createTRPCRouter, protectedProcedure } from "../trpc";

// Initialize unified payment service
const paymentConfig = createPaymentConfig({
  PAYMENT_GATEWAY_PRIMARY:
    (env.PAYMENT_GATEWAY_PRIMARY as "RAZORPAY" | "CASHFREE") || "RAZORPAY",
  PAYMENT_GATEWAY_FALLBACK: env.PAYMENT_GATEWAY_FALLBACK as
    | "RAZORPAY"
    | "CASHFREE",
  ENABLE_PAYMENT_GATEWAY_FALLBACK: env.ENABLE_PAYMENT_GATEWAY_FALLBACK,

  RAZORPAY_KEY_ID: env.RAZORPAY_KEY_ID,
  RAZORPAY_SECRET_KEY: env.RAZORPAY_SECRET_KEY,

  CASHFREE_APP_ID: env.CASHFREE_APP_ID,
  CASHFREE_SECRET_KEY: env.CASHFREE_SECRET_KEY,
  CASHFREE_ENVIRONMENT:
    (env.CASHFREE_ENVIRONMENT as "sandbox" | "production") || "sandbox",
});

const unifiedPaymentService = new UnifiedPaymentService(
  paymentConfig,
  analytics,
);

const customerManager = new CustomerManager({
  RAZORPAY_KEY_ID: env.RAZORPAY_KEY_ID,
  RAZORPAY_SECRET_KEY: env.RAZORPAY_SECRET_KEY,
  CASHFREE_APP_ID: env.CASHFREE_APP_ID,
  CASHFREE_SECRET_KEY: env.CASHFREE_SECRET_KEY,
  CASHFREE_ENVIRONMENT:
    (env.CASHFREE_ENVIRONMENT as "sandbox" | "production") || "sandbox",
});

// Minimum deposit amount (2,00,000 INR)
const MINIMUM_DEPOSIT_AMOUNT = 200000;

const DepositSchema = z.object({
  amount: z
    .number()
    .min(
      MINIMUM_DEPOSIT_AMOUNT,
      `Minimum deposit amount is ₹${MINIMUM_DEPOSIT_AMOUNT.toLocaleString()}`,
    ),
  currency: z.string().default("INR"),
});

const VerifyDepositSchema = z.object({
  orderId: z.string(),
  paymentId: z.string().optional(),
  signature: z.string().optional(),
  paymentGateway: z.enum(["RAZORPAY", "CASHFREE"]).default("CASHFREE"),
});

export const paymentRouter = createTRPCRouter({
  // Get current wallet balance and approval status
  getWalletInfo: protectedProcedure.query(async ({ ctx }) => {
    const { data: scraphubData, err } = await tryCatch(
      ctx.db.query.scraphubEmployee.findFirst({
        where: eq(scraphubEmployee.id, ctx.session.user.id),
        with: {
          scraphub: {
            columns: {
              walletBalance: true,
              adminApprovalStatus: true,
              adminApprovedAt: true,
              adminRejectionReason: true,
            },
          },
        },
      }),
    );

    if (err || !scraphubData || !scraphubData.scraphub) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "ScrapHub not found",
      });
    }

    return {
      walletBalance: Number(scraphubData.scraphub.walletBalance),
      adminApprovalStatus: scraphubData.scraphub.adminApprovalStatus,
      adminApprovedAt: scraphubData.scraphub.adminApprovedAt,
      adminRejectionReason: scraphubData.scraphub.adminRejectionReason,
      canDeposit: scraphubData.scraphub.adminApprovalStatus === "APPROVED",
      minimumDepositAmount: MINIMUM_DEPOSIT_AMOUNT,
    };
  }),

  // Create deposit order
  createDepositOrder: protectedProcedure
    .input(DepositSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if ScrapHub is approved
      const { data: scraphubData, err: scraphubErr } = await tryCatch(
        ctx.db.query.scraphubEmployee.findFirst({
          where: eq(scraphubEmployee.id, ctx.session.user.id!),
          with: {
            scraphub: {
              columns: {
                adminApprovalStatus: true,
                name: true,
                id: true,
                phoneNumber: true,
              },
            },
          },
        }),
      );

      if (scraphubErr || !scraphubData || !scraphubData.scraphub) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "ScrapHub not found",
        });
      }

      if (scraphubData.scraphub.adminApprovalStatus !== "APPROVED") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message:
            "Your account must be approved by admin before making deposits",
        });
      }

      const currentGateway = unifiedPaymentService.getCurrentGateway();

      // Create or ensure customer exists before creating order
      const customerData = {
        customerId: `scraphub_${scraphubData.scraphub.id}`,
        name: scraphubData.scraphub.name,
        email: ctx.session.user.email || undefined,
        phone: scraphubData.scraphub.phoneNumber || undefined,
      };

      const customerResult = await customerManager.ensureCustomer(
        customerData,
        currentGateway,
      );

      if (!customerResult.success) {
        console.warn(
          `Failed to create customer for ${currentGateway}:`,
          customerResult.error,
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create customer",
        });
        // Continue with order creation even if customer creation fails
        // as some gateways don't require pre-created customers
      }

      console.log("customerResult", customerResult);

      const cashfreeOrderId = `deposit_${Date.now()}`;

      const orderRequest = {
        amount: input.amount,
        currency: input.currency,
        receipt: cashfreeOrderId,
        customer_id: customerResult.customerId,
        notes: {
          scraphub_id: scraphubData.scraphub.id,
          scraphub_employee_id: ctx.session.user.id || "",
          transaction_for: "DEPOSIT",
          customer_name: scraphubData.scraphub.name,
          customer_email: ctx.session.user.email || "",
          customer_phone: scraphubData.scraphub.phoneNumber || "",
          description: `Deposit of ₹${input.amount.toLocaleString()}`,
        },
      };

      const { data: orderData, error: orderError } =
        await unifiedPaymentService.createOrder(orderRequest);

      if (orderError || !orderData) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create deposit order",
        });
      }

      console.log("orderData", orderData);

      // Prepare transaction data based on gateway
      const transactionData = {
        scraphubId: scraphubData.scraphub.id,
        amount: input.amount.toString(),
        paymentGateway: currentGateway,
        status: "PENDING",
        currency: orderData.currency,
        transactionFor: "DEPOSIT",
        transactionType: "CREDIT",
        description: `Deposit of ₹${input.amount.toLocaleString()}`,
        razorpayOrderId: "",
        cashfreeOrderId: "",
        gatewayMetadata: {
          payment_session_id: orderData.payment_session_id,
          order_created_at: new Date().toISOString(),
          gateway: currentGateway,
          order_amount: orderData.amount,
          order_currency: orderData.currency,
          order_status: orderData.status,
          razorpay_order_id: "",
          cashfree_order_id: "",
          cf_order_id: "",
          ...(orderData.notes && { order_notes: orderData.notes }),
        },
      };

      // Add gateway-specific fields
      if (currentGateway === "RAZORPAY") {
        transactionData.razorpayOrderId = orderData.id;
        // Add Razorpay-specific metadata
        transactionData.gatewayMetadata = {
          ...transactionData.gatewayMetadata,
          razorpay_order_id: orderData.id,
        };
      } else if (currentGateway === "CASHFREE") {
        transactionData.cashfreeOrderId = cashfreeOrderId;
        // Add Cashfree-specific metadata
        transactionData.gatewayMetadata = {
          ...transactionData.gatewayMetadata,
          cashfree_order_id: cashfreeOrderId,
          cf_order_id: orderData.id, // Cashfree internal order ID
        };
      }

      const { data: insertData, err: insertErr } = await tryCatch(
        ctx.db
          .insert(scraphubPaymentTransaction)
          .values(transactionData)
          .returning(),
      );

      if (insertErr || !insertData?.[0]) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create transaction record",
        });
      }

      const transactionId = insertData[0].id;

      // Track deposit initiation
      analytics.trackEvent("wallet_deposit_initiated", {
        user_id: ctx.session.user.id,
        scraphub_id: scraphubData.scraphub.id,
        user_type: "scraphub_employee",
        amount: input.amount,
        currency: input.currency,
        gateway: currentGateway,
        transaction_id: transactionId,
      });

      return {
        orderId: currentGateway === "RAZORPAY" ? orderData.id : cashfreeOrderId,
        transactionId: transactionId,
        name: "ScrapHub Deposit",
        amount:
          currentGateway === "RAZORPAY"
            ? Number(orderData.amount)
            : orderData.amount * 100, // Convert to paise for frontend compatibility
        currency: orderData.currency,
        description: `Deposit of ₹${input.amount.toLocaleString()} to ScrapHub wallet`,
        gateway: currentGateway,
        // Include payment_session_id for Cashfree
        payment_session_id:
          currentGateway === "CASHFREE"
            ? orderData.payment_session_id
            : undefined,
      };
    }),

  // Verify deposit payment
  verifyDeposit: protectedProcedure
    .input(VerifyDepositSchema)
    .mutation(async ({ ctx, input }) => {
      const { orderId } = input;

      // Get transaction details to determine gateway
      const { data: transaction, err: transactionErr } = await tryCatch(
        ctx.db.query.scraphubPaymentTransaction.findFirst({
          where: or(
            eq(scraphubPaymentTransaction.cashfreeOrderId, orderId),
            eq(scraphubPaymentTransaction.razorpayOrderId, orderId),
          ),
        }),
      );

      if (transactionErr || !transaction) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Transaction not found",
        });
      }

      // Verify payment based on gateway
      const verifyRequest = {
        orderId: orderId,
        paymentId: input.paymentId || "",
        signature: input.signature || "",
      };

      console.log("Payment verification request:", {
        orderId,
        gateway: transaction.paymentGateway,
        verifyRequest,
        inputData: {
          razorpayPaymentId: input.paymentId,
          cashfreePaymentId: input.paymentId,
          razorpaySignature: input.signature,
        },
      });

      const { data: verifyData, error: verifyError } =
        await unifiedPaymentService.verifyPayment(verifyRequest);

      console.log("Payment verification response:", {
        verified: verifyData?.verified,
        hasMetadata: !!verifyData?.metadata,
        error: verifyError?.message,
        metadata: verifyData?.metadata,
      });

      if (verifyError || !verifyData?.verified) {
        console.error("Payment verification failed:", {
          error: verifyError?.message,
          verified: verifyData?.verified,
          orderId,
          gateway: transaction.paymentGateway,
        });
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Payment verification failed",
        });
      }

      // Update transaction and wallet balance
      const { err: updateErr } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          // Prepare base metadata
          const baseMetadata = {
            verified_at: new Date().toISOString(),
            payment_method: transaction.paymentGateway,
            verification_successful: true,
          };

          // Merge existing metadata with new verification data and payment metadata
          const existingMetadata = transaction.gatewayMetadata || {};
          const newMetadata = {
            ...existingMetadata,
            ...baseMetadata,
            ...(verifyData.metadata && {
              payment_details: verifyData.metadata,
            }),
          };

          // Update transaction status
          const updateData: any = {
            status: "SUCCESS" as const,
            gatewayMetadata: newMetadata,
          };

          if (transaction.paymentGateway === "RAZORPAY") {
            updateData.razorpayPaymentId = input.paymentId;
            // Add Razorpay-specific metadata
            updateData.gatewayMetadata.razorpay_payment_id = input.paymentId;
          } else if (transaction.paymentGateway === "CASHFREE") {
            updateData.cashfreePaymentId = input.paymentId;
            // Add Cashfree-specific metadata
            updateData.gatewayMetadata.cashfree_payment_id = input.paymentId;
            updateData.gatewayMetadata.cf_payment_id = input.paymentId;
          }

          await tx
            .update(scraphubPaymentTransaction)
            .set(updateData)
            .where(eq(scraphubPaymentTransaction.id, transaction.id));

          await tx
            .update(scraphub)
            .set({
              walletBalance: sql`${scraphub.walletBalance} + ${transaction.amount}`,
            })
            .where(eq(scraphub.id, transaction.scraphubId));
        }),
      );

      if (updateErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update transaction",
        });
      }

      // Track successful deposit completion
      analytics.trackEvent("wallet_deposit_completed", {
        user_id: ctx.session.user.id,
        scraphub_id: transaction.scraphubId,
        user_type: "scraphub",
        amount: Number(transaction.amount),
        currency: transaction.currency || "INR",
        gateway: transaction.paymentGateway,
        transaction_id: transaction.id,
      });

      // Track wallet balance update
      analytics.trackWalletTransaction({
        user_id: ctx.session.user.id,
        // scraphub_id: transaction.scraphubId,
        user_type: "scraphub",
        transaction_type: "CREDIT",
        amount: Number(transaction.amount),
        currency: transaction.currency || "INR",
        balance_before: 0, // Would need to fetch previous balance
        balance_after: Number(transaction.amount), // Would need to fetch new balance
        transaction_for: "DEPOSIT",
      });

      return {
        success: true,
        message: "Deposit verified successfully",
        amount: Number(transaction.amount),
      };
    }),

  // Get transaction history
  getTransactionHistory: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(20),
        offset: z.number().min(0).default(0),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data: transactions, err } = await tryCatch(
        ctx.db.query.scraphubPaymentTransaction.findMany({
          where: eq(
            scraphubPaymentTransaction.scraphubId,
            ctx.session.user.scraphubId!,
          ),
          orderBy: (transactions, { desc }) => [desc(transactions.createdAt)],
          limit: input.limit,
          offset: input.offset,
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch transaction history",
        });
      }

      return {
        transactions:
          transactions?.map((t) => ({
            id: t.id,
            amount: Number(t.amount),
            currency: t.currency,
            status: t.status,
            transactionType: t.transactionType,
            transactionFor: t.transactionFor,
            description: t.description,
            paymentGateway: t.paymentGateway,
            createdAt: t.createdAt,
            updatedAt: t.updatedAt,
          })) || [],
      };
    }),

  // Debug endpoint to inspect transaction details
  getTransactionDetails: protectedProcedure
    .input(z.object({ transactionId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: transaction, err: transactionErr } = await tryCatch(
        ctx.db.query.scraphubPaymentTransaction.findFirst({
          where: eq(scraphubPaymentTransaction.id, input.transactionId),
        }),
      );

      if (transactionErr || !transaction) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Transaction not found",
        });
      }

      return {
        transaction,
        debug: {
          hasGatewayMetadata: !!transaction.gatewayMetadata,
          gatewayMetadataKeys: transaction.gatewayMetadata
            ? Object.keys(transaction.gatewayMetadata)
            : [],
          paymentGateway: transaction.paymentGateway,
          status: transaction.status,
          createdAt: transaction.createdAt,
          updatedAt: transaction.updatedAt,
        },
      };
    }),
});
