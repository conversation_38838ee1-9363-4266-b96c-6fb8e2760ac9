# Cashfree Payment Testing Guide

This guide will help you test the enhanced Cashfree payment verification flow for ScrapHub.

## What Was Fixed

✅ **Payment Session ID Storage**: Now properly stores `payment_session_id` from Cashfree order response  
✅ **Complete Metadata Storage**: Captures all payment details including payment method, timestamps, amounts  
✅ **Enhanced Verification**: Improved verification logic with better error handling and logging  
✅ **Debug Endpoints**: Added debugging capabilities to inspect transaction data  

## Testing Steps

### Step 1: Create a Deposit Order

1. **Login to ScrapHub** as a ScrapHub employee
2. **Navigate to wallet/deposit** section
3. **Create a deposit order** for any amount (e.g., ₹100)
4. **Note down the following from the response:**
   - `orderId` (Cashfree order ID)
   - `transactionId` (Internal transaction ID)
   - `payment_session_id` (Should now be present)

### Step 2: Check Order Creation Logs

Look for these logs in your console:

```
Cashfree order created successfully: {
  cf_order_id: "2196075510",
  order_id: "deposit_1755751821443", 
  payment_session_id: "session_LlFAjNtPZ3A9SOnWTle68uukkVbAzdIQ9aXUfH-KuKj...",
  order_status: "ACTIVE",
  order_amount: 200000
}
```

### Step 3: Inspect Transaction Metadata

Use the new debug endpoint to check if metadata was stored correctly:

```bash
# Replace with your actual transaction ID
curl -X POST "http://localhost:3000/api/trpc/scraphub.payment.getTransactionDetails" \
  -H "Content-Type: application/json" \
  -H "Cookie: your-session-cookie" \
  -d '{"json": {"transactionId": "your-transaction-id"}}'
```

**Expected Response:**
```json
{
  "result": {
    "data": {
      "json": {
        "transaction": {
          "id": "transaction-id",
          "gatewayMetadata": {
            "payment_session_id": "session_...",
            "order_created_at": "2025-08-21T...",
            "gateway": "CASHFREE",
            "cashfree_order_id": "2196075510",
            "cf_order_id": "2196075510"
          }
        },
        "debug": {
          "hasGatewayMetadata": true,
          "gatewayMetadataKeys": ["payment_session_id", "order_created_at", "gateway", ...]
        }
      }
    }
  }
}
```

### Step 4: Complete Payment via Cashfree

1. **Use the payment_session_id** to complete payment via Cashfree interface
2. **Note down the cf_payment_id** from Cashfree response
3. **Complete the payment** using test cards or UPI

### Step 5: Verify Payment

Call the verification endpoint:

```bash
curl -X POST "http://localhost:3000/api/trpc/scraphub.payment.verifyDeposit" \
  -H "Content-Type: application/json" \
  -H "Cookie: your-session-cookie" \
  -d '{
    "json": {
      "transactionId": "your-transaction-id",
      "cashfreeOrderId": "your-cashfree-order-id", 
      "cashfreePaymentId": "your-cf-payment-id"
    }
  }'
```

### Step 6: Check Verification Logs

Look for these logs during verification:

```
Cashfree verifyPayment request: {
  orderId: "deposit_1755751821443",
  paymentId: "cf_payment_id_here"
}

Cashfree getPaymentDetails response: {
  error: null,
  dataLength: 1,
  data: [{ cf_payment_id: "...", payment_status: "SUCCESS", ... }]
}

Found payment: {
  paymentFound: true,
  paymentStatus: "SUCCESS", 
  cf_payment_id: "cf_payment_id_here"
}

Payment verification request: {
  transactionId: "...",
  gateway: "CASHFREE",
  verifyRequest: { orderId: "...", paymentId: "...", signature: "" }
}

Payment verification response: {
  verified: true,
  hasMetadata: true,
  metadata: { cf_payment_id: "...", payment_status: "SUCCESS", ... }
}
```

### Step 7: Verify Final Transaction State

Use the debug endpoint again to check the final transaction state:

**Expected Final State:**
```json
{
  "transaction": {
    "status": "SUCCESS",
    "cashfreePaymentId": "cf_payment_id_here",
    "gatewayMetadata": {
      "payment_session_id": "session_...",
      "verified_at": "2025-08-21T...",
      "verification_successful": true,
      "cashfree_payment_id": "cf_payment_id_here",
      "payment_details": {
        "cf_payment_id": "cf_payment_id_here",
        "payment_status": "SUCCESS",
        "payment_method": { "upi": { "channel": "collect", "upi_id": "..." } },
        "payment_amount": 20000,
        "payment_currency": "INR",
        "bank_reference": "...",
        "auth_id": "...",
        "raw_payment_data": { /* complete payment object */ }
      }
    }
  }
}
```

## Troubleshooting

### Issue: Payment Session ID Missing
- **Check**: Order creation logs for `payment_session_id`
- **Solution**: Verify Cashfree order response structure

### Issue: Verification Fails
- **Check**: Verification logs for error details
- **Common causes**: 
  - Wrong `cf_payment_id`
  - Payment not completed in Cashfree
  - Network issues with Cashfree API

### Issue: Metadata Not Stored
- **Check**: Transaction debug endpoint
- **Solution**: Verify verification response contains metadata

## Test Scenarios

1. **Happy Path**: Order → Payment → Verification → Success
2. **Failed Payment**: Order → Failed Payment → Verification → Failure
3. **Wrong Payment ID**: Order → Payment → Wrong ID Verification → Failure
4. **Network Issues**: Test with network interruptions

## Monitoring

Monitor these key metrics:
- Order creation success rate
- Payment session ID presence
- Verification success rate  
- Metadata completeness
- Error rates and types

## Next Steps

After successful testing:
1. Remove debug logs from production
2. Set up monitoring and alerting
3. Document the new metadata structure
4. Train support team on new debugging capabilities
